<!-- Fixed Header with <PERSON> <PERSON><PERSON> -->
<div
  class="fixed top-0 left-0 right-0 z-50 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3"
>
  <div class="flex items-center justify-between max-w-4xl mx-auto">
    <div class="flex items-center space-x-3">
      <button
        (click)="goBack()"
        class="p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200 whitespace-nowrap"
        title="Back to Profiles"
      >
        <lucide-icon [img]="ArrowLeftIcon" class="w-6 h-6"></lucide-icon>
      </button>
      <h1 class="text-xl font-semibold text-gray-900 dark:text-gray-100">
        Edit Profile
      </h1>
    </div>

    <!-- Action Buttons in Header -->
    @if (profile) {
    <div class="flex items-center space-x-3">
      <button class="btn-primary flex items-center" (click)="saveProfile()">
        <lucide-icon [img]="SaveIcon" class="w-5 h-5 mr-2"></lucide-icon>
        {{ saveBtnText$ | async }}
      </button>
    </div>
    }
  </div>
</div>

<!-- Content with top padding to account for fixed header -->
@if (profile) {
<div class="pt-[5.5rem] p-6 max-w-4xl mx-auto space-y-6">
  <!-- Profile Form -->
  <div class="card">
    <div class="mb-6">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
        Basic Information
      </h2>
    </div>
    <div>
      <label
        for="profile-name-input"
        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
      >
        Profile Name
      </label>
      <div class="relative">
        <input
          id="profile-name-input"
          type="text"
          class="input-field pr-10"
          [(ngModel)]="profile.name"
          (ngModelChange)="onProfileFieldChange()"
          placeholder="Enter profile name"
        />
        <div
          class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none"
        >
          <lucide-icon
            [img]="EditIcon"
            class="w-5 h-5 text-gray-400"
          ></lucide-icon>
        </div>
      </div>
    </div>
  </div>
  <!-- Path Configuration -->
  <div class="card">
    <div class="mb-6">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
        Path Configuration
      </h2>
      <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
        Configure source and destination paths
      </p>
    </div>
    <div class="space-y-8">
      <!-- From Path -->
      <div>
        <h3 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Source Path
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label
              for="from-remote-select"
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >
              Remote
            </label>
            <select
              id="from-remote-select"
              class="select-field"
              [ngModel]="getFromRemote()"
              (ngModelChange)="updateFromPath($event, getFromPath())"
            >
              <option value="">Local</option>
              @for (remote of appService.remotes$ | async; track remote) {
              <option [value]="remote.name">
                {{ remote.name }}
              </option>
              }
            </select>
          </div>
          <div>
            <label
              for="from-path-input"
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >
              Path
            </label>
            <div class="relative">
              <input
                id="from-path-input"
                type="text"
                class="input-field pr-10"
                [ngModel]="getFromPath()"
                (ngModelChange)="updateFromPath(getFromRemote(), $event)"
                placeholder="/source/path"
              />
              <div
                class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none"
              >
                <lucide-icon
                  [img]="FolderOpenIcon"
                  class="w-5 h-5 text-gray-400"
                ></lucide-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- To Path -->
      <div>
        <h3 class="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Destination Path
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label
              for="to-remote-select"
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >
              Remote
            </label>
            <select
              id="to-remote-select"
              class="select-field"
              [ngModel]="getToRemote()"
              (ngModelChange)="updateToPath($event, getToPath())"
            >
              <option value="">Local</option>
              @for (remote of appService.remotes$ | async; track remote) {
              <option [value]="remote.name">
                {{ remote.name }}
              </option>
              }
            </select>
          </div>
          <div>
            <label
              for="to-path-input"
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >
              Path
            </label>
            <div class="relative">
              <input
                id="to-path-input"
                type="text"
                class="input-field pr-10"
                [ngModel]="getToPath()"
                (ngModelChange)="updateToPath(getToRemote(), $event)"
                placeholder="/destination/path"
              />
              <div
                class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none"
              >
                <lucide-icon
                  [img]="FolderOpenIcon"
                  class="w-5 h-5 text-gray-400"
                ></lucide-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Include/Exclude Paths -->
  <div class="card">
    <div class="mb-6">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
        Filter Settings
      </h2>
      <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
        Configure which files and folders to include or exclude from sync
      </p>
    </div>
    <!-- Include Paths -->
    <div class="mb-8">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-2">
          <lucide-icon
            [img]="CheckIcon"
            class="w-5 h-5 text-green-600"
          ></lucide-icon>
          <h3 class="text-md font-medium text-gray-900 dark:text-gray-100">
            Include Paths
          </h3>
        </div>
        <button
          type="button"
          class="btn-secondary flex items-center space-x-2"
          (click)="addIncludePath()"
        >
          <lucide-icon [img]="PlusIcon" class="w-4 h-4"></lucide-icon>
          <span>Add Path</span>
        </button>
      </div>
      @if (profile.included_paths.length === 0) {
      <div class="text-center py-8 text-gray-500 dark:text-gray-400">
        <lucide-icon
          [img]="CheckIcon"
          class="w-12 h-12 mx-auto mb-2 opacity-50"
        ></lucide-icon>
        <p>No include paths configured</p>
        <p class="text-sm">All files will be included by default</p>
      </div>
      } @if (profile.included_paths.length > 0) {
      <div class="space-y-3">
        @for (path of profile.included_paths; track $index; let i = $index) {
        <div
          class="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
        >
          <!-- Path Type Selector -->
          <div class="flex-shrink-0">
            <select
              class="select-field w-24"
              [value]="getIncludePathType(i)"
              (change)="onIncludePathTypeChange(i, $event)"
            >
              <option value="file">File</option>
              <option value="folder">Folder</option>
            </select>
          </div>
          <!-- Path Input -->
          <div class="flex-1 relative">
            <input
              type="text"
              class="input-field pr-10"
              [value]="getIncludePathValue(i)"
              (input)="onIncludePathValueChange(i, $event)"
              placeholder="Enter path pattern"
            />
            <div
              class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none"
            >
              <lucide-icon
                [img]="getIncludePathType(i) === 'file' ? FileIcon : FolderIcon"
                class="w-4 h-4 text-gray-400"
              ></lucide-icon>
            </div>
          </div>
          <!-- Remove Button -->
          <button
            type="button"
            class="flex-shrink-0 p-2 text-red-600 hover:text-red-800 hover:!bg-red-100 dark:hover:bg-red-900 rounded-lg transition-colors duration-200"
            (click)="removeIncludePath(i)"
            title="Remove path"
          >
            <lucide-icon [img]="MinusIcon" class="w-4 h-4"></lucide-icon>
          </button>
        </div>
        }
      </div>
      }
    </div>
    <!-- Exclude Paths -->
    <div class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-2">
          <lucide-icon [img]="XIcon" class="w-5 h-5 text-red-600"></lucide-icon>
          <h3 class="text-md font-medium text-gray-900 dark:text-gray-100">
            Exclude Paths
          </h3>
        </div>
        <button
          type="button"
          class="btn-secondary flex items-center space-x-2"
          (click)="addExcludePath()"
        >
          <lucide-icon [img]="PlusIcon" class="w-4 h-4"></lucide-icon>
          <span>Add Path</span>
        </button>
      </div>
      @if (profile.excluded_paths.length === 0) {
      <div class="text-center py-8 text-gray-500 dark:text-gray-400">
        <lucide-icon
          [img]="XIcon"
          class="w-12 h-12 mx-auto mb-2 opacity-50"
        ></lucide-icon>
        <p>No exclude paths configured</p>
        <p class="text-sm">No files will be excluded</p>
      </div>
      } @if (profile.excluded_paths.length > 0) {
      <div class="space-y-3">
        @for (path of profile.excluded_paths; track $index; let i = $index) {
        <div
          class="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
        >
          <!-- Path Type Selector -->
          <div class="flex-shrink-0">
            <select
              class="select-field w-24"
              [value]="getExcludePathType(i)"
              (change)="onExcludePathTypeChange(i, $event)"
            >
              <option value="file">File</option>
              <option value="folder">Folder</option>
            </select>
          </div>
          <!-- Path Input -->
          <div class="flex-1 relative">
            <input
              type="text"
              class="input-field pr-10"
              [value]="getExcludePathValue(i)"
              (input)="onExcludePathValueChange(i, $event)"
              placeholder="Enter path pattern"
            />
            <div
              class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none"
            >
              <lucide-icon
                [img]="getExcludePathType(i) === 'file' ? FileIcon : FolderIcon"
                class="w-4 h-4 text-gray-400"
              ></lucide-icon>
            </div>
          </div>
          <!-- Remove Button -->
          <button
            type="button"
            class="flex-shrink-0 p-2 text-red-600 hover:text-red-800 hover:!bg-red-100 dark:hover:bg-red-900 rounded-lg transition-colors duration-200"
            (click)="removeExcludePath(i)"
            title="Remove path"
          >
            <lucide-icon [img]="MinusIcon" class="w-4 h-4"></lucide-icon>
          </button>
        </div>
        }
      </div>
      }
    </div>
  </div>
  <!-- Performance Settings -->
  <div class="card">
    <div class="mb-6">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
        Performance Settings
      </h2>
      <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
        Configure parallel transfers and bandwidth limits
      </p>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <label
          for="parallel-select"
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
        >
          Parallel Transfers
        </label>
        <div class="relative">
          <select
            id="parallel-select"
            class="select-field pr-10"
            [(ngModel)]="profile.parallel"
            (ngModelChange)="onProfileFieldChange()"
          >
            @for (num of getNumberRange(1, 32); track num) {
            <option [value]="num">
              {{ num }}
            </option>
            }
          </select>
          <div
            class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none"
          >
            <lucide-icon
              [img]="ZapIcon"
              class="w-5 h-5 text-gray-400"
            ></lucide-icon>
          </div>
        </div>
      </div>
      <div>
        <label
          for="bandwidth-select"
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
        >
          Bandwidth Limit (MB/s)
        </label>
        <div class="relative">
          <select
            id="bandwidth-select"
            class="select-field pr-10"
            [(ngModel)]="profile.bandwidth"
            (ngModelChange)="onProfileFieldChange()"
          >
            @for (num of getNumberRange(1, 100); track num) {
            <option [value]="num">
              {{ num }}
            </option>
            }
          </select>
          <div
            class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none"
          >
            <lucide-icon
              [img]="WifiIcon"
              class="w-5 h-5 text-gray-400"
            ></lucide-icon>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
}

<!-- Loading State -->
@if (!profile) {
<div class="p-6 max-w-4xl mx-auto">
  <div class="card text-center">
    <div class="flex justify-center mb-4">
      <div
        class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"
      ></div>
    </div>
    <p class="text-gray-600 dark:text-gray-400">Loading profile...</p>
  </div>
</div>
}
