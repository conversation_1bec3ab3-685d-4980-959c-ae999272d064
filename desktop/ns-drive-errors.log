[ERROR] 2025/07/05 00:15:28 handlers.go:150: TraceID: 98145624-b4bb-42fb-af50-ed52813e5f07 | Code: INTERNAL_ERROR | Message: Internal server error [Context: [sync pull]]
[ERROR] 2025/07/05 00:15:28 handlers.go:154: TraceID: 98145624-b4bb-42fb-af50-ed52813e5f07 | Details: couldn't find root directory ID: context canceled
[ERROR] 2025/07/05 00:15:28 handlers.go:159: TraceID: 98145624-b4bb-42fb-af50-ed52813e5f07 | Stack Trace:
goroutine 14317 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x14000114e60, 0x14000561030, {0x1400264a0a0?, 0x1034588c4?, 0x10?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x14000114e60, {0x103fe11a0?, 0x1400264a080?}, {0x1400264a0a0, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleErrorWithTab(0x140001480c0, {0x103fe11a0, 0x1400264a080}, {0x1400031c4c0, 0x1b}, {0x1400264a0a0, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:107 +0xa8
desktop/backend.(*App).SyncWithTab.func4()
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:123 +0x388
created by desktop/backend.(*App).SyncWithTab in goroutine 14305
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:108 +0x948
[ERROR] 2025/07/05 00:15:28 handlers.go:164: TraceID: 98145624-b4bb-42fb-af50-ed52813e5f07 | Underlying Error: couldn't find root directory ID: context canceled
[ERROR] 2025/07/05 00:22:30 handlers.go:150: TraceID: 09062bbd-43ec-4fa8-835b-1d84968c821e | Code: INTERNAL_ERROR | Message: Internal server error [Context: [sync pull]]
[ERROR] 2025/07/05 00:22:30 handlers.go:154: TraceID: 09062bbd-43ec-4fa8-835b-1d84968c821e | Details: couldn't find root directory ID: context canceled
[ERROR] 2025/07/05 00:22:30 handlers.go:159: TraceID: 09062bbd-43ec-4fa8-835b-1d84968c821e | Stack Trace:
goroutine 23172 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x14000114e60, 0x14000520000, {0x1400034e920?, 0x1034588c4?, 0x10?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x14000114e60, {0x103fe11a0?, 0x1400034e900?}, {0x1400034e920, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleErrorWithTab(0x140001480c0, {0x103fe11a0, 0x1400034e900}, {0x1400031c0c0, 0x1b}, {0x1400034e920, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:107 +0xa8
desktop/backend.(*App).SyncWithTab.func4()
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:123 +0x388
created by desktop/backend.(*App).SyncWithTab in goroutine 23136
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:108 +0x948
[ERROR] 2025/07/05 00:22:30 handlers.go:164: TraceID: 09062bbd-43ec-4fa8-835b-1d84968c821e | Underlying Error: couldn't find root directory ID: context canceled
[ERROR] 2025/07/05 00:25:31 handlers.go:150: TraceID: a11c4d9d-8015-4738-b02a-aacdff1d9cb6 | Code: INTERNAL_ERROR | Message: Internal server error [Context: [sync pull]]
[ERROR] 2025/07/05 00:25:31 handlers.go:154: TraceID: a11c4d9d-8015-4738-b02a-aacdff1d9cb6 | Details: couldn't find root directory ID: context canceled
[ERROR] 2025/07/05 00:25:31 handlers.go:159: TraceID: a11c4d9d-8015-4738-b02a-aacdff1d9cb6 | Stack Trace:
goroutine 37719 [running]:
runtime/debug.Stack()
	/Users/<USER>/.goenv/versions/1.24.2/src/runtime/debug/stack.go:26 +0x64
desktop/backend/errors.(*ErrorHandler).logError(0x14000114e60, 0x140002b1dc0, {0x14000336140?, 0x1034588c4?, 0x10?})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:159 +0x238
desktop/backend/errors.(*ErrorHandler).Handle(0x14000114e60, {0x103fe11a0?, 0x14000336120?}, {0x14000336140, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/handlers.go:64 +0x90
desktop/backend/errors.(*Middleware).HandleErrorWithTab(0x140001480c0, {0x103fe11a0, 0x14000336120}, {0x140008ec140, 0x1b}, {0x14000336140, 0x2, 0x2})
	/Users/<USER>/Github/ns-drive/desktop/backend/errors/middleware.go:107 +0xa8
desktop/backend.(*App).SyncWithTab.func4()
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:123 +0x388
created by desktop/backend.(*App).SyncWithTab in goroutine 37733
	/Users/<USER>/Github/ns-drive/desktop/backend/commands.go:108 +0x948
[ERROR] 2025/07/05 00:25:31 handlers.go:164: TraceID: a11c4d9d-8015-4738-b02a-aacdff1d9cb6 | Underlying Error: couldn't find root directory ID: context canceled
